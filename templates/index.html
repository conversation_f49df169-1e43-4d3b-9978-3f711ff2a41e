<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat-back RFQ Comparison Tool</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>Chat-back RFQ Comparison Tool</h1>
            <p>Compare extracted RFQs with chat messages</p>
        </header>

        <!-- RFQ Panel (Top) -->
        <div class="panel rfq-panel">
            <div class="panel-header">
                <h2>RFQs</h2>
                <div class="controls">
                    <!-- Sorting Controls -->
                    <div class="sort-controls">
                        <label>Sort by:</label>
                        <select id="sortBy">
                            <option value="RFQ_startTime">Start Time</option>
                            <option value="confidence">Confidence</option>
                        </select>
                        <select id="sortOrder">
                            <option value="asc">Ascending</option>
                            <option value="desc">Descending</option>
                        </select>
                    </div>
                    
                    <!-- Filter Controls -->
                    <div class="filter-controls">
                        <select id="filterSales">
                            <option value="">All Sales</option>
                        </select>
                        <select id="filterCustomer">
                            <option value="">All Customers</option>
                        </select>
                        <select id="filterCurrencyPair">
                            <option value="">All Currency Pairs</option>
                        </select>
                        <select id="filterOutcome">
                            <option value="">All Outcomes</option>
                        </select>
                        <select id="filterConfidence">
                            <option value="">All Confidence</option>
                            <option value="0.9">Confidence > 0.9</option>
                            <option value="0.8">Confidence > 0.8</option>
                            <option value="0.7">Confidence > 0.7</option>
                            <option value="0.6">Confidence > 0.6</option>
                            <option value="0.5">Confidence > 0.5</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="panel-content">
                <div class="rfq-list" id="rfqList">
                    <!-- RFQ items will be populated here -->
                </div>
            </div>
        </div>

        <!-- Chat Panel (Bottom) -->
        <div class="panel chat-panel">
            <div class="panel-header">
                <h2>Chat Messages</h2>
                <div class="controls">
                    <div class="search-controls">
                        <input type="text" id="chatSearch" placeholder="Search chat messages...">
                        <button id="searchBtn">Search</button>
                        <button id="clearSearchBtn">Clear</button>
                    </div>
                </div>
            </div>
            
            <div class="panel-content">
                <div class="chat-list" id="chatList">
                    <!-- Chat messages will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit RFQ</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div class="form-group">
                        <label for="editCustomer">Customer:</label>
                        <input type="text" id="editCustomer" name="customer">
                    </div>
                    <div class="form-group">
                        <label for="editCurrencyPair">Currency Pair:</label>
                        <input type="text" id="editCurrencyPair" name="currency_pair">
                    </div>
                    <div class="form-group">
                        <label for="editAmount">Amount:</label>
                        <input type="text" id="editAmount" name="amount">
                    </div>
                    <div class="form-group">
                        <label for="editBase">Base:</label>
                        <input type="text" id="editBase" name="base">
                    </div>
                    <div class="form-group">
                        <label for="editDirection">Direction:</label>
                        <select id="editDirection" name="direction">
                            <option value="LHS">LHS</option>
                            <option value="RHS">RHS</option>
                            <option value="TWO_WAY">TWO_WAY</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editDates">Dates:</label>
                        <input type="text" id="editDates" name="dates">
                    </div>
                    <div class="form-group">
                        <label for="editPrice">Price:</label>
                        <input type="text" id="editPrice" name="price">
                    </div>
                    <div class="form-group">
                        <label for="editOutcome">Outcome:</label>
                        <select id="editOutcome" name="outcome">
                            <option value="done">Done</option>
                            <option value="passed">Passed</option>
                            <option value="dealt away">Dealt Away</option>
                            <option value="">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editConfidence">Confidence:</label>
                        <input type="number" id="editConfidence" name="confidence" min="0" max="1" step="0.01">
                    </div>
                    <div class="form-group">
                        <label for="editNotes">Notes:</label>
                        <textarea id="editNotes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="saveBtn" class="btn btn-primary">Save</button>
                <button type="button" id="cancelBtn" class="btn btn-secondary">Cancel</button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
