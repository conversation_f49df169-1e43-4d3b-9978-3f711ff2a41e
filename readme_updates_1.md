For the Chat-back application you have just authored please make the following changes:

RFQ Panel
    - have a header row with the field names rather than a head for each entry
    - the "Edit" button should be in a new column "edit" on the right hand side
    
Chat Messages Panel
    - have a header row with the field names rather than a head for each entry
    - show the date in a separate field
    - there should be a better contrast between SALES and TRADING messages while still being subtle

Edit RFQ Panel
    - this needs to be more compact / smaller fonts, need to be able to edit without scrolling
    - no need to confirm the update when saving - we want this to be quick and east

Overall app
    - the fonts used should be smaller and consistent across the appl  ication - we are trying to fit in a lot of information
    - the header sections should be narrower / more compact
    - I dont need the top header  
    - A better layout I want to try is side by side with the RFQ panel taking up ~70% of the screen and the chat panel \
      taking up ~30% of the screen (make this easily configurable in template/sylesheet as appropriate)


