# Chat-back RFQ Comparison Tool - Usage Guide

## Overview
The Chat-back RFQ Comparison Tool is a web application that allows you to compare extracted RFQs with chat messages to verify the accuracy of RFQ extraction.

## Getting Started

### Prerequisites
- Python 3.7+
- pip

### Installation
1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Start the application:
   ```bash
   python app.py
   ```

3. Open your web browser and navigate to:
   ```
   http://localhost:5001
   ```

## Features

### RFQ Panel (Top)
- **View RFQs**: Displays all RFQs with key information including start time, sales person, trader, customer, currency pair, amount, base currency, direction, dates, price, outcome, and confidence level.
- **Sort**: Sort RFQs by start time or confidence level in ascending or descending order.
- **Filter**: Filter RFQs by:
  - Sales person
  - Customer
  - Currency pair
  - Outcome
  - Confidence level (>0.9, >0.8, >0.7, etc.)
- **Select RFQ**: Click on any RFQ to highlight related chat messages in the bottom panel.
- **Edit RFQ**: Click the "Edit" button to modify RFQ details.

### Chat Panel (Bottom)
- **View Messages**: Displays all chat messages with timestamp, desk (SALES/TRADING), user, and content.
- **Color Coding**: 
  - Sales messages: Green background
  - Trading messages: Blue background
- **Highlighting**: When an RFQ is selected, related chat messages are highlighted in yellow.
- **Search**: Search for specific content within chat messages.
- **Auto-scroll**: When an RFQ is selected, the chat panel automatically scrolls to the first related message.

### Edit Functionality
- **In-place Editing**: Modify the following RFQ fields:
  - Customer
  - Currency Pair
  - Amount
  - Base Currency
  - Direction (LHS, RHS, TWO_WAY)
  - Dates
  - Price
  - Outcome
  - Confidence (0-1)
  - Notes
- **Visual Indicators**: Updated fields are highlighted with a yellow background.
- **Change Tracking**: All changes are saved to `db/updated_rfqs.json` without modifying the original data.

## Data Files

### Input Files (Read-only)
- `db/rfqs.json`: Original RFQ data
- `db/chats.json`: Chat message data

### Output File
- `db/updated_rfqs.json`: Stores all user modifications (created automatically when you make edits)

## Usage Tips

1. **Finding Related Messages**: Click on an RFQ to see which chat messages were used to extract it. The related messages will be highlighted and the view will scroll to show them.

2. **Filtering for Quality**: Use the confidence filter to focus on RFQs that may need review (e.g., confidence < 0.8).

3. **Searching Chats**: Use the search function to find specific terms or phrases in chat messages.

4. **Editing RFQs**: When you find discrepancies between the RFQ and chat messages, use the edit function to correct the RFQ data. Your changes are tracked separately from the original data.

5. **Visual Cues**: 
   - Orange left border: RFQ has been updated
   - Yellow highlighting: Updated fields within an RFQ
   - Yellow highlighting in chat: Messages related to selected RFQ

## Technical Details

### Architecture
- **Backend**: Flask (Python)
- **Frontend**: Vanilla JavaScript with modern CSS
- **Data Storage**: JSON files
- **Styling**: Clean, modern financial application design

### API Endpoints
- `GET /`: Main application page
- `GET /api/rfqs`: Get all RFQs with updates applied
- `GET /api/chats`: Get all chat messages
- `GET /api/chats/search?q=<term>`: Search chat messages
- `GET /api/rfqs/<start>/<time>/chats`: Get chats for specific RFQ
- `POST /api/rfqs/update`: Save RFQ updates
- `GET /api/filters/values`: Get filter dropdown values

## Troubleshooting

### Port Issues
If port 5001 is in use, modify the port in `app.py`:
```python
app.run(debug=True, host='0.0.0.0', port=5002)  # Change to available port
```

### Data Issues
- Ensure `db/rfqs.json` and `db/chats.json` exist and contain valid JSON
- Check browser console for JavaScript errors
- Check Flask console for server errors

### Performance
- The application loads all data into memory for fast filtering and searching
- For very large datasets, consider implementing pagination or server-side filtering
