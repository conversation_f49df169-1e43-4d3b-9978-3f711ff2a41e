/* Chat-back RFQ Comparison Tool Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #2c3e50;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 300;
}

header p {
    font-size: 1.1em;
    opacity: 0.9;
}

.panel {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.panel-header {
    background: #34495e;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.panel-header h2 {
    font-size: 1.5em;
    font-weight: 400;
}

.controls {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.sort-controls, .filter-controls, .search-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.sort-controls label {
    font-weight: 500;
}

select, input[type="text"], input[type="number"], textarea {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    background: white;
}

select:focus, input:focus, textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
}

button {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

#searchBtn, #clearSearchBtn {
    background: #3498db;
    color: white;
}

#searchBtn:hover, #clearSearchBtn:hover {
    background: #2980b9;
}

.panel-content {
    height: 400px;
    overflow-y: auto;
}

.rfq-panel .panel-content {
    height: 450px;
}

.chat-panel .panel-content {
    height: 350px;
}

/* RFQ List Styles */
.rfq-item {
    padding: 15px 20px;
    border-bottom: 1px solid #ecf0f1;
    cursor: pointer;
    transition: all 0.3s ease;
    display: grid;
    grid-template-columns: 120px 80px 80px 100px 80px 100px 80px 80px 100px 80px 80px 1fr;
    gap: 15px;
    align-items: center;
    font-size: 13px;
}

.rfq-item:hover {
    background-color: #f8f9fa;
}

.rfq-item.selected {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.rfq-item.updated {
    background-color: #fff3e0;
    border-left: 4px solid #ff9800;
}

.rfq-field {
    display: flex;
    flex-direction: column;
}

.rfq-field-label {
    font-weight: 600;
    color: #7f8c8d;
    font-size: 11px;
    margin-bottom: 2px;
}

.rfq-field-value {
    color: #2c3e50;
    word-break: break-word;
}

.rfq-field-value.updated-field {
    background-color: #ffeb3b;
    padding: 2px 4px;
    border-radius: 3px;
}

.confidence-high { color: #27ae60; font-weight: 600; }
.confidence-medium { color: #f39c12; font-weight: 600; }
.confidence-low { color: #e74c3c; font-weight: 600; }

.edit-btn {
    background: #3498db;
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
}

.edit-btn:hover {
    background: #2980b9;
}

/* Chat List Styles */
.chat-item {
    padding: 10px 20px;
    border-bottom: 1px solid #ecf0f1;
    display: grid;
    grid-template-columns: 100px 80px 120px 1fr;
    gap: 15px;
    align-items: center;
    font-size: 13px;
}

.chat-item.sales {
    background-color: #e8f5e8;
    border-left: 3px solid #4caf50;
}

.chat-item.trading {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.chat-item.highlighted {
    background-color: #fff9c4;
    border-left: 4px solid #ffc107;
    font-weight: 500;
}

.chat-field {
    display: flex;
    flex-direction: column;
}

.chat-field-label {
    font-weight: 600;
    color: #7f8c8d;
    font-size: 11px;
    margin-bottom: 2px;
}

.chat-field-value {
    color: #2c3e50;
    word-break: break-word;
}

.chat-time {
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 600px;
    max-width: 90%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: #34495e;
    color: white;
    padding: 20px;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-weight: 400;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #34495e;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.modal-footer {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #ecf0f1;
    background: #f8f9fa;
    border-radius: 0 0 10px 10px;
}

.modal-footer button {
    margin-left: 10px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .rfq-item {
        grid-template-columns: repeat(6, 1fr);
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .panel-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .controls {
        width: 100%;
        justify-content: center;
    }
    
    .rfq-item {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .chat-item {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}
