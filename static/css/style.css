/* Chat-back RFQ Comparison Tool Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #2c3e50;
    line-height: 1.4;
    font-size: 12px;
}

.container {
    max-width: 100%;
    margin: 0;
    padding: 10px;
    display: flex;
    gap: 10px;
    height: 100vh;
}

/* Remove header styles - not needed */

.panel {
    background: white;
    border-radius: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Configurable panel widths - adjust these percentages as needed */
.rfq-panel {
    width: 70%;
    min-width: 600px;
}

.chat-panel {
    width: 30%;
    min-width: 300px;
}

.panel-header {
    background: #34495e;
    color: white;
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    min-height: 40px;
}

.panel-header h2 {
    font-size: 14px;
    font-weight: 500;
    margin: 0;
}

.controls {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.sort-controls, .filter-controls, .search-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.sort-controls label {
    font-weight: 500;
}

select, input[type="text"], input[type="number"], textarea {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 11px;
    background: white;
}

select:focus, input:focus, textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 3px rgba(102, 126, 234, 0.3);
}

button {
    padding: 4px 8px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

#searchBtn, #clearSearchBtn {
    background: #3498db;
    color: white;
}

#searchBtn:hover, #clearSearchBtn:hover {
    background: #2980b9;
}

.panel-content {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

/* RFQ List Styles */
.rfq-list {
    display: table;
    width: 100%;
    font-size: 11px;
}

.rfq-header {
    display: table-header-group;
    background: #f8f9fa;
    font-weight: 600;
    color: #34495e;
}

.rfq-header-row {
    display: table-row;
}

.rfq-header-cell {
    display: table-cell;
    padding: 6px 4px;
    border-bottom: 2px solid #dee2e6;
    text-align: left;
    font-size: 10px;
    font-weight: 600;
}

.rfq-body {
    display: table-row-group;
}

.rfq-item {
    display: table-row;
    cursor: pointer;
    transition: all 0.2s ease;
}

.rfq-item:hover {
    background-color: #f8f9fa;
}

.rfq-item.selected {
    background-color: #e3f2fd;
}

.rfq-item.updated {
    background-color: #fff3e0;
}

.rfq-cell {
    display: table-cell;
    padding: 4px;
    border-bottom: 1px solid #ecf0f1;
    vertical-align: top;
    word-break: break-word;
    font-size: 11px;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.rfq-cell.updated-field {
    background-color: #ffeb3b;
}

.edit-btn {
    background: #3498db;
    color: white;
    padding: 2px 6px;
    font-size: 10px;
    border-radius: 3px;
    border: none;
    cursor: pointer;
}

.edit-btn:hover {
    background: #2980b9;
}

.confidence-high { color: #27ae60; font-weight: 600; }
.confidence-medium { color: #f39c12; font-weight: 600; }
.confidence-low { color: #e74c3c; font-weight: 600; }

.edit-btn {
    background: #3498db;
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
}

.edit-btn:hover {
    background: #2980b9;
}

/* Chat List Styles */
.chat-list {
    display: table;
    width: 100%;
    font-size: 11px;
}

.chat-header {
    display: table-header-group;
    background: #f8f9fa;
    font-weight: 600;
    color: #34495e;
}

.chat-header-row {
    display: table-row;
}

.chat-header-cell {
    display: table-cell;
    padding: 6px 4px;
    border-bottom: 2px solid #dee2e6;
    text-align: left;
    font-size: 10px;
    font-weight: 600;
}

.chat-body {
    display: table-row-group;
}

.chat-item {
    display: table-row;
}

.chat-item.sales {
    background-color: #f0f8f0;
}

.chat-item.trading {
    background-color: #f0f4f8;
}

.chat-item.highlighted {
    background-color: #fff9c4 !important;
    font-weight: 500;
}

.chat-cell {
    display: table-cell;
    padding: 3px 4px;
    border-bottom: 1px solid #ecf0f1;
    vertical-align: top;
    word-break: break-word;
    font-size: 11px;
}

.chat-cell:nth-child(1) { width: 80px; } /* Date */
.chat-cell:nth-child(2) { width: 70px; } /* Time */
.chat-cell:nth-child(3) { width: 60px; } /* Desk */
.chat-cell:nth-child(4) { width: 80px; } /* User */
.chat-cell:nth-child(5) { width: auto; } /* Content */

.chat-time {
    font-family: 'Courier New', monospace;
    font-size: 10px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 5px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.modal-header {
    background: #34495e;
    color: white;
    padding: 10px 15px;
    border-radius: 5px 5px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-weight: 500;
    font-size: 14px;
}

.close {
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 15px;
    max-height: 70vh;
    overflow-y: auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.form-group {
    margin-bottom: 8px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: block;
    margin-bottom: 2px;
    font-weight: 600;
    color: #34495e;
    font-size: 10px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 4px 6px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 11px;
}

.form-group textarea {
    resize: vertical;
    min-height: 40px;
}

.modal-footer {
    padding: 10px 15px;
    text-align: right;
    border-top: 1px solid #ecf0f1;
    background: #f8f9fa;
    border-radius: 0 0 5px 5px;
}

.modal-footer button {
    margin-left: 8px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .rfq-item {
        grid-template-columns: repeat(6, 1fr);
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .panel-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .controls {
        width: 100%;
        justify-content: center;
    }
    
    .rfq-item {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .chat-item {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}
