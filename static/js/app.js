// Chat-back RFQ Comparison Tool JavaScript

class ChatBackApp {
    constructor() {
        this.rfqs = [];
        this.chats = [];
        this.filteredRfqs = [];
        this.selectedRfq = null;
        this.highlightedMessageIds = [];
        this.currentEditRfq = null;
        
        this.init();
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.populateFilterOptions();
        this.renderRfqs();
        this.renderChats();
    }

    async loadData() {
        try {
            // Load RFQs
            const rfqResponse = await fetch('/api/rfqs');
            const rfqData = await rfqResponse.json();
            if (rfqData.success) {
                this.rfqs = rfqData.data;
                this.filteredRfqs = [...this.rfqs];
            }

            // Load Chats
            const chatResponse = await fetch('/api/chats');
            const chatData = await chatResponse.json();
            if (chatData.success) {
                this.chats = chatData.data;
            }
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }

    setupEventListeners() {
        // Sorting controls
        document.getElementById('sortBy').addEventListener('change', () => this.applySortAndFilter());
        document.getElementById('sortOrder').addEventListener('change', () => this.applySortAndFilter());

        // Filter controls
        document.getElementById('filterSales').addEventListener('change', () => this.applySortAndFilter());
        document.getElementById('filterCustomer').addEventListener('change', () => this.applySortAndFilter());
        document.getElementById('filterCurrencyPair').addEventListener('change', () => this.applySortAndFilter());
        document.getElementById('filterOutcome').addEventListener('change', () => this.applySortAndFilter());
        document.getElementById('filterConfidence').addEventListener('change', () => this.applySortAndFilter());

        // Search controls
        document.getElementById('searchBtn').addEventListener('click', () => this.searchChats());
        document.getElementById('clearSearchBtn').addEventListener('click', () => this.clearSearch());
        document.getElementById('chatSearch').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.searchChats();
        });

        // Modal controls
        document.querySelector('.close').addEventListener('click', () => this.closeModal());
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());
        document.getElementById('saveBtn').addEventListener('click', () => this.saveRfqUpdate());

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('editModal');
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    async populateFilterOptions() {
        try {
            const response = await fetch('/api/filters/values');
            const data = await response.json();
            if (data.success) {
                this.populateSelect('filterSales', data.data.sales);
                this.populateSelect('filterCustomer', data.data.customer);
                this.populateSelect('filterCurrencyPair', data.data.currency_pair);
                this.populateSelect('filterOutcome', data.data.outcome);
            }
        } catch (error) {
            console.error('Error loading filter options:', error);
        }
    }

    populateSelect(selectId, options) {
        const select = document.getElementById(selectId);
        const currentValue = select.value;
        
        // Clear existing options except the first one
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }
        
        // Add new options
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option;
            optionElement.textContent = option;
            select.appendChild(optionElement);
        });
        
        // Restore previous selection if it still exists
        if (options.includes(currentValue)) {
            select.value = currentValue;
        }
    }

    applySortAndFilter() {
        let filtered = [...this.rfqs];

        // Apply filters
        const salesFilter = document.getElementById('filterSales').value;
        const customerFilter = document.getElementById('filterCustomer').value;
        const currencyPairFilter = document.getElementById('filterCurrencyPair').value;
        const outcomeFilter = document.getElementById('filterOutcome').value;
        const confidenceFilter = document.getElementById('filterConfidence').value;

        if (salesFilter) {
            filtered = filtered.filter(rfq => rfq.sales === salesFilter);
        }
        if (customerFilter) {
            filtered = filtered.filter(rfq => rfq.customer === customerFilter);
        }
        if (currencyPairFilter) {
            filtered = filtered.filter(rfq => rfq.currency_pair === currencyPairFilter);
        }
        if (outcomeFilter) {
            filtered = filtered.filter(rfq => rfq.outcome === outcomeFilter);
        }
        if (confidenceFilter) {
            const minConfidence = parseFloat(confidenceFilter);
            filtered = filtered.filter(rfq => rfq.confidence > minConfidence);
        }

        // Apply sorting
        const sortBy = document.getElementById('sortBy').value;
        const sortOrder = document.getElementById('sortOrder').value;

        filtered.sort((a, b) => {
            let aVal = a[sortBy];
            let bVal = b[sortBy];

            if (sortBy === 'RFQ_startTime') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            }

            if (aVal < bVal) return sortOrder === 'asc' ? -1 : 1;
            if (aVal > bVal) return sortOrder === 'asc' ? 1 : -1;
            return 0;
        });

        this.filteredRfqs = filtered;
        this.renderRfqs();
    }

    renderRfqs() {
        const rfqList = document.getElementById('rfqList');
        rfqList.innerHTML = '';

        this.filteredRfqs.forEach(rfq => {
            const rfqElement = this.createRfqElement(rfq);
            rfqList.appendChild(rfqElement);
        });
    }

    createRfqElement(rfq) {
        const div = document.createElement('div');
        div.className = `rfq-item ${rfq.is_updated ? 'updated' : ''}`;
        div.dataset.rfqStart = rfq.RFQ_start;
        div.dataset.rfqStartTime = rfq.RFQ_startTime;

        const formatTime = (timeStr) => {
            try {
                return new Date(timeStr).toLocaleString();
            } catch {
                return timeStr;
            }
        };

        const getConfidenceClass = (confidence) => {
            if (confidence >= 0.9) return 'confidence-high';
            if (confidence >= 0.7) return 'confidence-medium';
            return 'confidence-low';
        };

        const formatAmount = (amount) => {
            if (Array.isArray(amount)) {
                return amount.map(item => item.amount || '').join(', ');
            }
            return amount || '';
        };

        const formatDates = (dates) => {
            if (Array.isArray(dates)) {
                if (dates.length > 0 && Array.isArray(dates[0])) {
                    return dates.map(datePair => datePair.join('-')).join(', ');
                }
                return dates.join(', ');
            }
            return dates || '';
        };

        const formatPrice = (price) => {
            if (Array.isArray(price)) {
                return price.join(', ');
            }
            return price || '';
        };

        div.innerHTML = `
            <div class="rfq-field">
                <div class="rfq-field-label">Start Time</div>
                <div class="rfq-field-value">${formatTime(rfq.RFQ_startTime)}</div>
            </div>
            <div class="rfq-field">
                <div class="rfq-field-label">Sales</div>
                <div class="rfq-field-value ${rfq.updated_fields_list.includes('sales') ? 'updated-field' : ''}">${rfq.sales}</div>
            </div>
            <div class="rfq-field">
                <div class="rfq-field-label">Trader</div>
                <div class="rfq-field-value ${rfq.updated_fields_list.includes('trader') ? 'updated-field' : ''}">${rfq.trader}</div>
            </div>
            <div class="rfq-field">
                <div class="rfq-field-label">Customer</div>
                <div class="rfq-field-value ${rfq.updated_fields_list.includes('customer') ? 'updated-field' : ''}">${rfq.customer}</div>
            </div>
            <div class="rfq-field">
                <div class="rfq-field-label">Currency</div>
                <div class="rfq-field-value ${rfq.updated_fields_list.includes('currency_pair') ? 'updated-field' : ''}">${rfq.currency_pair}</div>
            </div>
            <div class="rfq-field">
                <div class="rfq-field-label">Amount</div>
                <div class="rfq-field-value ${rfq.updated_fields_list.includes('amount') ? 'updated-field' : ''}">${formatAmount(rfq.amount)}</div>
            </div>
            <div class="rfq-field">
                <div class="rfq-field-label">Base</div>
                <div class="rfq-field-value ${rfq.updated_fields_list.includes('base') ? 'updated-field' : ''}">${rfq.base}</div>
            </div>
            <div class="rfq-field">
                <div class="rfq-field-label">Direction</div>
                <div class="rfq-field-value ${rfq.updated_fields_list.includes('direction') ? 'updated-field' : ''}">${rfq.direction}</div>
            </div>
            <div class="rfq-field">
                <div class="rfq-field-label">Dates</div>
                <div class="rfq-field-value ${rfq.updated_fields_list.includes('dates') ? 'updated-field' : ''}">${formatDates(rfq.dates)}</div>
            </div>
            <div class="rfq-field">
                <div class="rfq-field-label">Price</div>
                <div class="rfq-field-value ${rfq.updated_fields_list.includes('price') ? 'updated-field' : ''}">${formatPrice(rfq.price)}</div>
            </div>
            <div class="rfq-field">
                <div class="rfq-field-label">Outcome</div>
                <div class="rfq-field-value ${rfq.updated_fields_list.includes('outcome') ? 'updated-field' : ''}">${rfq.outcome}</div>
            </div>
            <div class="rfq-field">
                <div class="rfq-field-label">Confidence</div>
                <div class="rfq-field-value ${getConfidenceClass(rfq.confidence)} ${rfq.updated_fields_list.includes('confidence') ? 'updated-field' : ''}">${rfq.confidence.toFixed(2)}</div>
            </div>
            <div class="rfq-field">
                <button class="edit-btn" onclick="app.openEditModal(${rfq.RFQ_start}, '${rfq.RFQ_startTime}')">Edit</button>
            </div>
        `;

        div.addEventListener('click', (e) => {
            if (!e.target.classList.contains('edit-btn')) {
                this.selectRfq(rfq);
            }
        });

        return div;
    }

    async selectRfq(rfq) {
        // Remove previous selection
        document.querySelectorAll('.rfq-item.selected').forEach(item => {
            item.classList.remove('selected');
        });

        // Add selection to current item
        const rfqElement = document.querySelector(`[data-rfq-start="${rfq.RFQ_start}"][data-rfq-start-time="${rfq.RFQ_startTime}"]`);
        if (rfqElement) {
            rfqElement.classList.add('selected');
        }

        this.selectedRfq = rfq;
        await this.highlightRelatedChats(rfq);
    }

    async highlightRelatedChats(rfq) {
        try {
            const response = await fetch(`/api/rfqs/${rfq.RFQ_start}/${encodeURIComponent(rfq.RFQ_startTime)}/chats`);
            const data = await response.json();
            
            if (data.success) {
                this.highlightedMessageIds = data.rfq_info.message_ids;
                this.renderChats();
                
                // Scroll to first related message
                const firstMessageId = Math.min(...this.highlightedMessageIds);
                const firstMessageElement = document.querySelector(`[data-message-id="${firstMessageId}"]`);
                if (firstMessageElement) {
                    firstMessageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        } catch (error) {
            console.error('Error highlighting related chats:', error);
        }
    }

    renderChats() {
        const chatList = document.getElementById('chatList');
        chatList.innerHTML = '';

        this.chats.forEach(chat => {
            const chatElement = this.createChatElement(chat);
            chatList.appendChild(chatElement);
        });
    }

    createChatElement(chat) {
        const div = document.createElement('div');
        const deskClass = chat.desk.toLowerCase();
        const isHighlighted = this.highlightedMessageIds.includes(chat.message_id);
        
        div.className = `chat-item ${deskClass} ${isHighlighted ? 'highlighted' : ''}`;
        div.dataset.messageId = chat.message_id;

        const formatTime = (timeStr) => {
            try {
                const date = new Date(timeStr);
                return date.toLocaleTimeString();
            } catch {
                return timeStr;
            }
        };

        div.innerHTML = `
            <div class="chat-field">
                <div class="chat-field-label">Time</div>
                <div class="chat-field-value chat-time">${formatTime(chat.eventTime)}</div>
            </div>
            <div class="chat-field">
                <div class="chat-field-label">Desk</div>
                <div class="chat-field-value">${chat.desk}</div>
            </div>
            <div class="chat-field">
                <div class="chat-field-label">User</div>
                <div class="chat-field-value">${chat.loginName}</div>
            </div>
            <div class="chat-field">
                <div class="chat-field-label">Content</div>
                <div class="chat-field-value">${chat.content}</div>
            </div>
        `;

        return div;
    }

    async searchChats() {
        const searchTerm = document.getElementById('chatSearch').value.trim();
        if (!searchTerm) return;

        try {
            const response = await fetch(`/api/chats/search?q=${encodeURIComponent(searchTerm)}`);
            const data = await response.json();
            
            if (data.success) {
                this.chats = data.data;
                this.highlightedMessageIds = []; // Clear RFQ highlights
                this.renderChats();
            }
        } catch (error) {
            console.error('Error searching chats:', error);
        }
    }

    async clearSearch() {
        document.getElementById('chatSearch').value = '';
        await this.loadData(); // Reload all chats
        this.renderChats();
    }

    openEditModal(rfqStart, rfqStartTime) {
        const rfq = this.rfqs.find(r => r.RFQ_start === rfqStart && r.RFQ_startTime === rfqStartTime);
        if (!rfq) return;

        this.currentEditRfq = rfq;

        // Populate form fields
        document.getElementById('editCustomer').value = rfq.customer || '';
        document.getElementById('editCurrencyPair').value = rfq.currency_pair || '';
        document.getElementById('editAmount').value = this.getDisplayValue(rfq.amount);
        document.getElementById('editBase').value = rfq.base || '';
        document.getElementById('editDirection').value = rfq.direction || '';
        document.getElementById('editDates').value = this.getDisplayValue(rfq.dates);
        document.getElementById('editPrice').value = this.getDisplayValue(rfq.price);
        document.getElementById('editOutcome').value = rfq.outcome || '';
        document.getElementById('editConfidence').value = rfq.confidence || '';
        document.getElementById('editNotes').value = rfq.notes || '';

        document.getElementById('editModal').style.display = 'block';
    }

    getDisplayValue(value) {
        if (Array.isArray(value)) {
            if (value.length > 0 && typeof value[0] === 'object') {
                return JSON.stringify(value);
            }
            return value.join(', ');
        }
        return value || '';
    }

    closeModal() {
        document.getElementById('editModal').style.display = 'none';
        this.currentEditRfq = null;
    }

    async saveRfqUpdate() {
        if (!this.currentEditRfq) return;

        const formData = new FormData(document.getElementById('editForm'));
        const updatedFields = {};
        let hasChanges = false;

        // Check for changes and build updated fields object
        const fields = ['customer', 'currency_pair', 'amount', 'base', 'direction', 'dates', 'price', 'outcome', 'confidence'];
        
        fields.forEach(field => {
            const newValue = formData.get(field);
            const currentValue = this.getDisplayValue(this.currentEditRfq[field]);
            
            if (newValue !== currentValue) {
                updatedFields[field] = field === 'confidence' ? parseFloat(newValue) : newValue;
                hasChanges = true;
            }
        });

        if (!hasChanges) {
            this.closeModal();
            return;
        }

        const updateData = {
            rfq_start: this.currentEditRfq.RFQ_start,
            rfq_start_time: this.currentEditRfq.RFQ_startTime,
            updated_fields: updatedFields,
            notes: formData.get('notes')
        };

        try {
            const response = await fetch('/api/rfqs/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            });

            const result = await response.json();
            
            if (result.success) {
                this.closeModal();
                await this.loadData(); // Reload data to show updates
                this.applySortAndFilter(); // Reapply current filters
                alert('RFQ updated successfully!');
            } else {
                alert('Error updating RFQ: ' + result.error);
            }
        } catch (error) {
            console.error('Error saving RFQ update:', error);
            alert('Error saving update');
        }
    }
}

// Initialize the application
const app = new ChatBackApp();
