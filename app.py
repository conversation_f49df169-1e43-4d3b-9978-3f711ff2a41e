"""
Flask application for Chat-back RFQ comparison tool.
"""
from flask import Flask, render_template, request, jsonify
from datetime import datetime
from database import Database
from models import RFQUpdate

app = Flask(__name__)
db = Database()


@app.route('/')
def index():
    """Main page route."""
    return render_template('index.html')


@app.route('/api/rfqs')
def get_rfqs():
    """Get all RFQs with any updates applied."""
    try:
        rfqs = db.get_rfqs_with_updates()
        return jsonify({'success': True, 'data': rfqs})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/chats')
def get_chats():
    """Get all chat messages."""
    try:
        chats = db.load_chats()
        chat_data = [chat.to_dict() for chat in chats]
        return jsonify({'success': True, 'data': chat_data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/chats/search')
def search_chats():
    """Search chat messages by content."""
    try:
        search_term = request.args.get('q', '')
        if not search_term:
            return jsonify({'success': False, 'error': 'Search term required'}), 400
        
        matching_chats = db.search_chat_messages(search_term)
        chat_data = [chat.to_dict() for chat in matching_chats]
        return jsonify({'success': True, 'data': chat_data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/rfqs/<int:rfq_start>/<rfq_start_time>/chats')
def get_rfq_chats(rfq_start, rfq_start_time):
    """Get chat messages for a specific RFQ."""
    try:
        # Find the RFQ
        rfqs = db.load_rfqs()
        target_rfq = None
        for rfq in rfqs:
            if rfq.RFQ_start == rfq_start and rfq.RFQ_startTime == rfq_start_time:
                target_rfq = rfq
                break
        
        if not target_rfq:
            return jsonify({'success': False, 'error': 'RFQ not found'}), 404
        
        # Get related chat messages
        related_chats = db.get_chat_messages_for_rfq(target_rfq)
        chat_data = [chat.to_dict() for chat in related_chats]
        
        return jsonify({
            'success': True, 
            'data': chat_data,
            'rfq_info': {
                'message_ids': target_rfq.message_ids,
                'start_time': target_rfq.RFQ_startTime,
                'end_time': target_rfq.RFQ_endTime
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/rfqs/update', methods=['POST'])
def update_rfq():
    """Update an RFQ."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['rfq_start', 'rfq_start_time', 'updated_fields']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400
        
        # Create RFQ update
        rfq_update = RFQUpdate(
            rfq_start=data['rfq_start'],
            rfq_start_time=data['rfq_start_time'],
            updated_fields=data['updated_fields'],
            notes=data.get('notes', ''),
            timestamp=datetime.utcnow().isoformat() + 'Z'
        )
        
        # Save update
        success = db.save_update(rfq_update)
        
        if success:
            return jsonify({'success': True, 'message': 'RFQ updated successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to save update'}), 500
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/filters/values')
def get_filter_values():
    """Get unique values for filter dropdowns."""
    try:
        rfqs = db.load_rfqs()
        
        # Extract unique values for filters
        sales_values = list(set(rfq.sales for rfq in rfqs))
        customer_values = list(set(rfq.customer for rfq in rfqs))
        currency_pair_values = list(set(rfq.currency_pair for rfq in rfqs))
        outcome_values = list(set(rfq.outcome for rfq in rfqs if rfq.outcome))
        
        return jsonify({
            'success': True,
            'data': {
                'sales': sorted(sales_values),
                'customer': sorted(customer_values),
                'currency_pair': sorted(currency_pair_values),
                'outcome': sorted(outcome_values)
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
