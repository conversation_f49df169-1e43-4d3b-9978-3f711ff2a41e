I've got some further changes for the Chat-Back application

1. for the RFQ panel - make the header row frozen when you scroll

2. For the Chat panel - when highlight SALES and TRADING rows (selected RFQ)
   - keep the SALES yellow highlighting as is
   - change the TRADING yellow highlight to be a lighter hue of yellow

3. For the Chat panel I want the following additional functionality
    - have the ability to toggle "non RFQ rows"

    when this is toggled I want all chat messages that are not a part of any RFQ \
    to be highlighted in a very light red hue - so the text is still clearly readable.

    This will help identify potential RFQs that may have been missed during the chat analysis.
