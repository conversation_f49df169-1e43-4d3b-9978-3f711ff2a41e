"""
Data models for the Chat-back application.
"""
from dataclasses import dataclass
from typing import List, Optional, Union, Any
from datetime import datetime


@dataclass
class RFQ:
    """RFQ data model."""
    RFQ_start: int
    RFQ_end: int
    RFQ_startTime: str
    RFQ_endTime: str
    sales: str
    trader: str
    customer: str
    amount: Union[str, List[dict]]
    currency_pair: str
    base: str
    dates: Union[List[str], List[List[str]]]
    direction: str
    price: Union[str, List[str]]
    outcome: str
    confidence: float
    notes: str
    message_ids: List[int]

    @classmethod
    def from_dict(cls, data: dict) -> 'RFQ':
        """Create RFQ instance from dictionary."""
        return cls(**data)

    def to_dict(self) -> dict:
        """Convert RFQ instance to dictionary."""
        return {
            'RFQ_start': self.RFQ_start,
            'RFQ_end': self.RFQ_end,
            'RFQ_startTime': self.RFQ_startTime,
            'RFQ_endTime': self.RFQ_endTime,
            'sales': self.sales,
            'trader': self.trader,
            'customer': self.customer,
            'amount': self.amount,
            'currency_pair': self.currency_pair,
            'base': self.base,
            'dates': self.dates,
            'direction': self.direction,
            'price': self.price,
            'outcome': self.outcome,
            'confidence': self.confidence,
            'notes': self.notes,
            'message_ids': self.message_ids
        }

    def get_display_amount(self) -> str:
        """Get amount as display string."""
        if isinstance(self.amount, list):
            return ', '.join([f"{item.get('amount', '')}" for item in self.amount])
        return str(self.amount)

    def get_display_dates(self) -> str:
        """Get dates as display string."""
        if isinstance(self.dates, list) and len(self.dates) > 0:
            if isinstance(self.dates[0], list):
                return ', '.join(['-'.join(date_pair) for date_pair in self.dates])
            else:
                return ', '.join(self.dates)
        return str(self.dates)

    def get_display_price(self) -> str:
        """Get price as display string."""
        if isinstance(self.price, list):
            return ', '.join(self.price)
        return str(self.price)


@dataclass
class ChatMessage:
    """Chat message data model."""
    desk: str
    loginName: str
    message_id: int
    eventTime: str
    content: str

    @classmethod
    def from_dict(cls, data: dict) -> 'ChatMessage':
        """Create ChatMessage instance from dictionary."""
        return cls(**data)

    def to_dict(self) -> dict:
        """Convert ChatMessage instance to dictionary."""
        return {
            'desk': self.desk,
            'loginName': self.loginName,
            'message_id': self.message_id,
            'eventTime': self.eventTime,
            'content': self.content
        }

    def get_formatted_time(self) -> str:
        """Get formatted time for display."""
        try:
            dt = datetime.fromisoformat(self.eventTime.replace('Z', '+00:00'))
            return dt.strftime('%H:%M:%S.%f')[:-3]  # Show milliseconds
        except:
            return self.eventTime


@dataclass
class RFQUpdate:
    """RFQ update tracking model."""
    rfq_start: int
    rfq_start_time: str
    updated_fields: dict
    notes: str
    timestamp: str

    @classmethod
    def from_dict(cls, data: dict) -> 'RFQUpdate':
        """Create RFQUpdate instance from dictionary."""
        return cls(**data)

    def to_dict(self) -> dict:
        """Convert RFQUpdate instance to dictionary."""
        return {
            'rfq_start': self.rfq_start,
            'rfq_start_time': self.rfq_start_time,
            'updated_fields': self.updated_fields,
            'notes': self.notes,
            'timestamp': self.timestamp
        }
