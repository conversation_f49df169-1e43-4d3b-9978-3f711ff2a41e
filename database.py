"""
Database operations for the Chat-back application.
"""
import json
import os
from typing import List, Optional
from datetime import datetime
from models import RFQ, ChatMessage, RFQUpdate


class Database:
    """Database operations for JSON files."""
    
    def __init__(self, db_path: str = 'db'):
        self.db_path = db_path
        self.rfqs_file = os.path.join(db_path, 'rfqs.json')
        self.chats_file = os.path.join(db_path, 'chats.json')
        self.updates_file = os.path.join(db_path, 'updated_rfqs.json')

    def load_rfqs(self) -> List[RFQ]:
        """Load RFQs from JSON file."""
        try:
            with open(self.rfqs_file, 'r') as f:
                data = json.load(f)
                return [RFQ.from_dict(item) for item in data]
        except FileNotFoundError:
            return []
        except json.JSONDecodeError:
            return []

    def load_chats(self) -> List[ChatMessage]:
        """Load chat messages from JSON file."""
        try:
            with open(self.chats_file, 'r') as f:
                data = json.load(f)
                return [ChatMessage.from_dict(item) for item in data]
        except FileNotFoundError:
            return []
        except json.JSONDecodeError:
            return []

    def load_updates(self) -> List[RFQUpdate]:
        """Load RFQ updates from JSON file."""
        try:
            with open(self.updates_file, 'r') as f:
                data = json.load(f)
                return [RFQUpdate.from_dict(item) for item in data]
        except FileNotFoundError:
            return []
        except json.JSONDecodeError:
            return []

    def save_update(self, rfq_update: RFQUpdate) -> bool:
        """Save an RFQ update to the updates file."""
        try:
            # Load existing updates
            updates = self.load_updates()
            
            # Check if update for this RFQ already exists
            existing_index = None
            for i, update in enumerate(updates):
                if (update.rfq_start == rfq_update.rfq_start and 
                    update.rfq_start_time == rfq_update.rfq_start_time):
                    existing_index = i
                    break
            
            # Update existing or add new
            if existing_index is not None:
                updates[existing_index] = rfq_update
            else:
                updates.append(rfq_update)
            
            # Save to file
            with open(self.updates_file, 'w') as f:
                json.dump([update.to_dict() for update in updates], f, indent=2)
            
            return True
        except Exception as e:
            print(f"Error saving update: {e}")
            return False

    def get_rfqs_with_updates(self) -> List[dict]:
        """Get RFQs with any updates applied."""
        rfqs = self.load_rfqs()
        updates = self.load_updates()
        
        # Create a mapping of updates by RFQ identifier
        updates_map = {}
        for update in updates:
            key = (update.rfq_start, update.rfq_start_time)
            updates_map[key] = update
        
        # Apply updates to RFQs
        result = []
        for rfq in rfqs:
            rfq_dict = rfq.to_dict()
            key = (rfq.RFQ_start, rfq.RFQ_startTime)
            
            if key in updates_map:
                update = updates_map[key]
                # Apply updated fields
                for field, value in update.updated_fields.items():
                    rfq_dict[field] = value
                # Mark as updated
                rfq_dict['is_updated'] = True
                rfq_dict['updated_fields_list'] = list(update.updated_fields.keys())
                # Update notes if provided
                if update.notes:
                    rfq_dict['notes'] = update.notes
            else:
                rfq_dict['is_updated'] = False
                rfq_dict['updated_fields_list'] = []
            
            result.append(rfq_dict)
        
        return result

    def get_chat_messages_for_rfq(self, rfq: RFQ) -> List[ChatMessage]:
        """Get chat messages related to a specific RFQ."""
        all_chats = self.load_chats()
        
        # Filter messages by message_id and time range
        related_messages = []
        for chat in all_chats:
            if (chat.message_id in rfq.message_ids and
                chat.message_id >= rfq.RFQ_start and
                chat.message_id <= rfq.RFQ_end):
                related_messages.append(chat)
        
        return related_messages

    def search_chat_messages(self, search_term: str) -> List[ChatMessage]:
        """Search chat messages by content."""
        all_chats = self.load_chats()
        search_term = search_term.lower()
        
        matching_messages = []
        for chat in all_chats:
            if search_term in chat.content.lower():
                matching_messages.append(chat)
        
        return matching_messages
